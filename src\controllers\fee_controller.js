const { createConnection } = require('../database');
const _TABLE = 'reservations';
const moment = require('moment');

exports.get = async (req, res) => {
    let sql;
    let connection;
    try {
        // Create connection
        connection = createConnection();
        // Build query based on parameters
        if (req.query.init_date && req.query.end_date) {
            sql = `SELECT createdAt, id_reservation, folio, member_id, total_payment, base_price, fee_rci, fee_tr, payment_id FROM ${_TABLE} WHERE (createdAt BETWEEN '${req.query.init_date}' AND '${req.query.end_date}');`;
        } else {
            sql = `SELECT createdAt, id_reservation, folio, member_id, total_payment, base_price, fee_rci, fee_tr, payment_id FROM ${_TABLE};`;
        }
        // Execute query
        const [result] = await connection.promise().query(sql);

        // Format dates if results exist
        if(result.length > 0){
            result.forEach((item) => {
                if (item.createdAt) {
                    item.createdAt = moment(item.createdAt).format('YYYY-MM-DD');
                }
            });
        }

        // Send response
        res.status(200).json({ results: result });

    } catch (error) {
        console.error('Database error:', error);
        res.status(500).json({ error: 'Database error occurred' });
    } finally {
        if (connection) {
            connection.end();
        }
    }
};