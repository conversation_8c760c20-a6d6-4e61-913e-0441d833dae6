require('dotenv').config();

const express = require('express');
const morgan = require('morgan');
const app = express();
const cors = require('cors');
const bodyParser = require('body-parser');

// Configuración de CORS
const corsOptions = {
    origin: '*', // Reemplaza con tu dominio frontend
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
};

// Middleware para encabezados CORS personalizados
app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS,CONNECT,TRACE');
    res.setHeader(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization, X-Content-Type-Options, Accept, X-Requested-With, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'
    );
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Private-Network', true);
    res.setHeader('Access-Control-Max-Age', 7200); // 2 horas para preflight requests
    next();
});

// Otros middlewares
app.use(cors(corsOptions));
// middlewares
app.use(morgan('dev'));
app.use(express.json());

app.set('port', process.env.PORT);
// routes
app.use('/api/v1', require('./src/routes/units'));
app.use('/api/v1', require('./src/routes/airlines'));
app.use('/api/v1', require('./src/routes/zones'));
app.use('/api/v1', require('./src/routes/places'));
app.use('/api/v1', require('./src/routes/locations'));
app.use('/api/v1', require('./src/routes/reservations'));
app.use('/api/v1', require('./src/routes/auth.js'))
app.use('/api/v1', require('./src/routes/users'));
app.use('/api/v1', require('./src/routes/email'));
app.use('/api/v1', require('./src/routes/rates'));
app.use('/api/v1', require('./src/routes/stripe'));
app.use('/api/v1', require('./src/routes/fees'));

//module.exports = app;
module.exports = app;