import React, { useContext, useEffect, useState } from 'react';
import styles from './summary-price.module.scss';
import { ReservationContext } from '../../context/reservation.context';
import { Spinner } from 'reactstrap';

const SummaryPrice = ({ reserve, isServiceActive, totalPayment, serviceSelected, isCalculatingRate, tarifaBase }) => {
    const { reservation } = useContext(ReservationContext);
    const { has_promotion: hasPromotion, promotion } = reservation;
    const [regularRate, setRegularRate] = useState(0);
    const [promotionRate, setPromotionRate] = useState(0);
    const [extraServicePrice, setExtraServicePrice] = useState(0);
    const [promotionPercent, setPromotionPercent] = useState(promotion);
    const [baseTransportRate, setBaseTransportRate] = useState(0);
    
    // console.log('tarifaBase', tarifaBase);

    // Efecto para actualizar el precio del servicio extra
    useEffect(() => {
      if (isServiceActive && serviceSelected && serviceSelected.price) {
        // Aseguramos que el precio del servicio extra sea un número
        setExtraServicePrice(Number(serviceSelected.price));
      } else {
        setExtraServicePrice(0);
      }
    }, [isServiceActive, serviceSelected]);

    // Efecto para calcular el precio base de transporte (sin servicio extra)
    useEffect(() => {
      // Si hay un servicio activo, restamos su precio del total para obtener el precio base de transporte
      if (isServiceActive && serviceSelected && serviceSelected.price && totalPayment > 0) {
        const transportRate = Number(totalPayment) - Number(serviceSelected.price);
        setBaseTransportRate(Number(transportRate));
      } else {
        // Si no hay servicio activo, el precio base es igual al totalPayment
        // Aseguramos que baseTransportRate sea siempre un número
        setBaseTransportRate(Number(totalPayment));
      }
    }, [totalPayment, isServiceActive, serviceSelected]);

    // Efecto para calcular el precio regular y el precio con promoción
    useEffect(() => {
      if (baseTransportRate > 0) {
        // El precio regular es el precio base de transporte
        setRegularRate(Number(baseTransportRate));
        setPromotionRate(Number(baseTransportRate));
      } else {
        setRegularRate(0);
        setPromotionRate(0);
      }
    }, [baseTransportRate, hasPromotion, promotionPercent, isServiceActive]);

    // Efecto para actualizar el porcentaje de promoción cuando cambia en el contexto
    useEffect(() => {
      setPromotionPercent(Number(promotion));
    }, [promotion]);

  return (
    <div className={styles.reservationSummary}>
      <div className={styles.summaryServiceTitle}>
        <h4 className={styles.itemTitle}>
          Service: <span>{reserve.trip_type}</span>
          {isCalculatingRate && <Spinner size="sm" color="primary" className="ml-2" />}
        </h4>
      </div>
        {hasPromotion && (
            <div className={`${styles.summaryItem}`}>
                <h5 className={styles.itemTitle}>Promotion: <span className={styles.brown}>{promotionPercent}%</span> Off</h5>
            </div>
        )}
         {totalPayment !== 0 && (<>
            <div className={styles.summaryItem}>
                <span className={styles.itemTitle}>Rate:</span>
                <span className={ hasPromotion ? `${styles.itemValue} ${styles.originalPrice}` : `${styles.itemValue}`}>
                  ${tarifaBase || reserve.base_price || reserve.total_payment} USD
                </span>
            </div>
            { hasPromotion && (
              <div className={styles.summaryItem}>
                  <span className={styles.itemTitle}>Promotion Rate:</span>
                  <span className={`${styles.itemValue} ${styles.brown}`}>
                    ${Math.round(promotionRate)} USD
                  </span>
              </div>
              )
            }
         </>)}
      {isServiceActive && serviceSelected && (
        <>
        <hr />
        <div className={styles.extra_service_active}>
            <div>
                <h5 className={styles.itemTitle}>Extra Service:</h5>
                <div className={`${styles.itemValue} ${styles.name}`}>
                  {serviceSelected.name || "Stop at the Supermarket or Grocery Store"}
                  <p>{serviceSelected.time}</p>
                </div>
            </div>
            <div className={`${styles.itemTitle} ${styles.price}`}>
              ${Math.round(Number(extraServicePrice))} USD
            </div>
        </div>
        <hr />
        </>
      )}
      <div className={styles.summaryItem}>
        <span className={`${styles.itemTitle} ${styles.totalTitle}`}>Total:</span>
        {
          hasPromotion ?
          <span className={`${styles.itemValue} ${styles.totalPrice}`}>
            ${Math.round(isServiceActive
              ? Number(promotionRate) + Number(extraServicePrice)
              : Number(promotionRate))} USD
          </span>
          :
          <span className={`${styles.itemValue} ${styles.totalPrice}`}>
            ${Math.round(isServiceActive
              ? Number(regularRate) + Number(extraServicePrice)
              : Number(regularRate))} USD
          </span>
        }
      </div>
    </div>
  );
};

SummaryPrice.defaultProps = {
  serviceSelected: {}
};

export default SummaryPrice;
