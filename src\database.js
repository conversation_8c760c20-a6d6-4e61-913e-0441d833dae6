let mysql = require('mysql2');

exports.createConnection = function(){
    // Verificar que las variables de entorno estén definidas
    if (!process.env.ENVIRONMENT) {
        throw new Error('ENVIRONMENT variable is not defined');
    }

    // Seleccionar configuración basada en el entorno
    let host, user, password, database;
    if(process.env.ENVIRONMENT === 'DEVELOPMENT'){
        if (!process.env.DEV_DB_HOST || !process.env.DEV_DB_USER || !process.env.DEV_DB_PASS || !process.env.DEV_DB_NAME) {
            throw new Error('Missing development database environment variables');
        }
        host = process.env.DEV_DB_HOST;
        user = process.env.DEV_DB_USER;
        password = process.env.DEV_DB_PASS;
        database = process.env.DEV_DB_NAME;
    } else {
        if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASS || !process.env.DB_NAME) {
            throw new Error('Missing production database environment variables');
        }
        host = process.env.DB_HOST;
        user = process.env.DB_USER;
        password = process.env.DB_PASS;
        database = process.env.DB_NAME;
    }
  
    let client = mysql.createConnection({
      host: host,
      user: user,
      password: password,
      database: database
    });

    // Verificar la conexión
    client.connect((err) => {
        if (err) {
            console.error('Error connecting to database:', err);
            throw err;
        }
        console.log('Connected to database');
    });

    return client;
};